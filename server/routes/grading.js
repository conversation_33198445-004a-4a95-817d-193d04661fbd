const express = require('express');
const { prisma } = require('../config/prisma');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取待批阅的答卷列表
 */
router.get('/pending', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            exam_id,
            question_type,
            page = 1,
            limit = 20
        } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        const where = {
            status: 'submitted'
        };

        if (exam_id) {
            where.exam_id = parseInt(exam_id);
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher') {
            where.exam = {
                creator_id: req.user.user_id
            };
        }

        // 获取待批阅的答卷
        const [studentScores, total] = await Promise.all([
            prisma.studentScore.findMany({
                where,
                include: {
                    exam: {
                        select: {
                            exam_name: true,
                            course: {
                                select: {
                                    course_name: true
                                }
                            }
                        }
                    },
                    student: {
                        select: {
                            username: true,
                            full_name: true
                        }
                    },
                    student_answers: {
                        include: {
                            question: {
                                select: {
                                    question_text: true,
                                    question_type: true
                                }
                            }
                        },
                        where: question_type ? {
                            question: {
                                question_type: question_type
                            }
                        } : undefined
                    }
                },
                orderBy: {
                    submission_time: 'desc'
                },
                skip: offset,
                take: parseInt(limit)
            }),
            prisma.studentScore.count({ where })
        ]);

        res.json({
            success: true,
            data: {
                student_scores: studentScores,
                pagination: {
                    current_page: parseInt(page),
                    per_page: parseInt(limit),
                    total,
                    total_pages: Math.ceil(total / parseInt(limit))
                }
            }
        });

    } catch (error) {
        console.error('获取待批阅答卷错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取单个学生的答卷详情
 */
router.get('/answer/:scoreId', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const scoreId = parseInt(req.params.scoreId);

        const studentScore = await prisma.studentScore.findUnique({
            where: {
                score_id: scoreId
            },
            include: {
                exam: {
                    include: {
                        course: {
                            select: {
                                course_name: true
                            }
                        },
                        examQuestions: {
                            include: {
                                question: {
                                    include: {
                                        options: true
                                    }
                                }
                            },
                            orderBy: {
                                exam_question_id: 'asc'
                            }
                        }
                    }
                },
                student: {
                    select: {
                        username: true,
                        full_name: true,
                        email: true
                    }
                },
                student_answers: {
                    include: {
                        question: {
                            include: {
                                options: true
                            }
                        }
                    }
                }
            }
        });

        if (!studentScore) {
            return res.status(404).json({
                success: false,
                message: '答卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && studentScore.exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        res.json({
            success: true,
            data: studentScore
        });

    } catch (error) {
        console.error('获取答卷详情错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 批阅单题 - 给单个题目打分
 */
router.put('/score-question', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            answer_id,
            score,
            comment
        } = req.body;

        if (!answer_id || score === undefined) {
            return res.status(400).json({
                success: false,
                message: '答案ID和分数不能为空'
            });
        }

        // 获取答案详情
        const studentAnswer = await prisma.studentAnswer.findUnique({
            where: {
                answer_id: parseInt(answer_id)
            },
            include: {
                student_score: {
                    include: {
                        exam: true
                    }
                },
                question: true
            }
        });

        if (!studentAnswer) {
            return res.status(404).json({
                success: false,
                message: '答案不存在'
            });
        }

        // 如果是教师，只能批阅自己创建的试卷
        if (req.user.role === 'teacher' && studentAnswer.student_score.exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取该题的满分
        const examQuestion = await prisma.examQuestion.findFirst({
            where: {
                exam_id: studentAnswer.student_score.exam_id,
                question_id: studentAnswer.question_id
            }
        });

        if (!examQuestion) {
            return res.status(404).json({
                success: false,
                message: '试卷题目不存在'
            });
        }

        // 验证分数范围
        if (parseFloat(score) < 0 || parseFloat(score) > examQuestion.score) {
            return res.status(400).json({
                success: false,
                message: `分数必须在 0 到 ${examQuestion.score} 之间`
            });
        }

        // 更新答案分数和批阅状态
        await prisma.studentAnswer.update({
            where: {
                answer_id: parseInt(answer_id)
            },
            data: {
                score: parseFloat(score),
                is_marked: true,
                comment: comment || null
            }
        });

        res.json({
            success: true,
            message: '批阅成功'
        });

    } catch (error) {
        console.error('批阅题目错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 完成整份答卷批阅
 */
router.put('/complete/:scoreId', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const scoreId = parseInt(req.params.scoreId);

        // 获取学生成绩记录
        const studentScore = await prisma.studentScore.findUnique({
            where: {
                score_id: scoreId
            },
            include: {
                exam: true,
                student_answers: true
            }
        });

        if (!studentScore) {
            return res.status(404).json({
                success: false,
                message: '成绩记录不存在'
            });
        }

        // 如果是教师，只能操作自己创建的试卷
        if (req.user.role === 'teacher' && studentScore.exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 检查是否所有主观题都已批阅
        const subjectiveAnswers = await prisma.studentAnswer.findMany({
            where: {
                score_id: scoreId,
                question: {
                    question_type: {
                        in: ['fill_in_blank', 'essay']
                    }
                }
            }
        });

        const unmarkedCount = subjectiveAnswers.filter(answer => !answer.is_marked).length;
        if (unmarkedCount > 0) {
            return res.status(400).json({
                success: false,
                message: `还有 ${unmarkedCount} 道主观题未批阅`
            });
        }

        // 计算总分
        const totalScore = studentScore.student_answers.reduce((sum, answer) => {
            return sum + (answer.score || 0);
        }, 0);

        // 更新学生成绩状态
        await prisma.studentScore.update({
            where: {
                score_id: scoreId
            },
            data: {
                total_score: totalScore,
                status: 'marked'
            }
        });

        res.json({
            success: true,
            message: '批阅完成',
            data: {
                total_score: totalScore
            }
        });

    } catch (error) {
        console.error('完成批阅错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取教师的阅卷统计信息
 */
router.get('/statistics', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const { exam_id } = req.query;

        // 构建查询条件
        const where = {
            status: {
                in: ['submitted', 'marked']
            }
        };

        if (exam_id) {
            where.exam_id = parseInt(exam_id);
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher') {
            where.exam = {
                creator_id: req.user.user_id
            };
        }

        const [
            totalSubmitted,
            totalMarked,
            pendingGrading
        ] = await Promise.all([
            // 总提交数
            prisma.studentScore.count({
                where: {
                    ...where,
                    status: {
                        in: ['submitted', 'marked']
                    }
                }
            }),
            // 已批阅数
            prisma.studentScore.count({
                where: {
                    ...where,
                    status: 'marked'
                }
            }),
            // 待批阅的主观题数量
            prisma.studentAnswer.count({
                where: {
                    is_marked: false,
                    question: {
                        question_type: {
                            in: ['fill_in_blank', 'essay']
                        }
                    },
                    student_score: where
                }
            })
        ]);

        res.json({
            success: true,
            data: {
                total_submitted: totalSubmitted,
                total_marked: totalMarked,
                pending_grading: pendingGrading,
                completion_rate: totalSubmitted > 0 ? Math.round(totalMarked / totalSubmitted * 100) : 0
            }
        });

    } catch (error) {
        console.error('获取阅卷统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 批量自动批阅客观题
 */
router.post('/auto-grade/:examId', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.examId);

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能操作自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取所有已提交但未批阅的客观题答案
        const objectiveAnswers = await prisma.studentAnswer.findMany({
            where: {
                is_marked: false,
                question: {
                    question_type: {
                        in: ['single_choice', 'multiple_choice', 'true_false']
                    }
                },
                student_score: {
                    exam_id: examId,
                    status: 'submitted'
                }
            },
            include: {
                question: true,
                student_score: {
                    include: {
                        exam: {
                            include: {
                                examQuestions: {
                                    where: {
                                        question_id: {
                                            in: []
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        let gradedCount = 0;

        // 批量处理客观题自动批阅
        for (const answer of objectiveAnswers) {
            const question = answer.question;
            const studentAnswer = answer.student_answer;
            const correctAnswer = question.correct_answer;

            // 获取该题的分值
            const examQuestion = await prisma.examQuestion.findFirst({
                where: {
                    exam_id: examId,
                    question_id: question.question_id
                }
            });

            if (!examQuestion) continue;

            let score = 0;

            // 根据题型判断答案是否正确
            if (question.question_type === 'single_choice' || question.question_type === 'true_false') {
                // 单选题和判断题
                score = studentAnswer === correctAnswer ? examQuestion.score : 0;
            } else if (question.question_type === 'multiple_choice') {
                // 多选题 - 完全正确才得分
                const studentAnswers = studentAnswer ? studentAnswer.split(',').sort() : [];
                const correctAnswers = correctAnswer ? correctAnswer.split(',').sort() : [];

                const isCorrect = studentAnswers.length === correctAnswers.length &&
                    studentAnswers.every(ans => correctAnswers.includes(ans));

                score = isCorrect ? examQuestion.score : 0;
            }

            // 更新答案分数和批阅状态
            await prisma.studentAnswer.update({
                where: {
                    answer_id: answer.answer_id
                },
                data: {
                    score: score,
                    is_marked: true
                }
            });

            gradedCount++;
        }

        res.json({
            success: true,
            message: `成功自动批阅 ${gradedCount} 道客观题`,
            data: {
                graded_count: gradedCount
            }
        });

    } catch (error) {
        console.error('自动批阅错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
