<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>在线考试系统</h1>
        <p>欢迎登录</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="userStore.isLoading"
            @click.prevent="handleLogin"
            native-type="button"
          >
            {{ userStore.isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <div class="test-accounts">
          <h4>测试账号</h4>
          <div class="account-list">
            <div class="account-item">
              <span class="role">教师:</span>
              <span class="username">teacher1</span>
              <span class="password">teacher123</span>
              <el-button size="small" @click.prevent="quickLogin('teacher1', 'teacher123')" native-type="button">
                快速登录
              </el-button>
            </div>
            <div class="account-item">
              <span class="role">学生:</span>
              <span class="username">student1</span>
              <span class="password">student123</span>
              <el-button size="small" @click.prevent="quickLogin('student1', 'student123')" native-type="button">
                快速登录
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

import { useUserStore } from '@/stores/user'
import type { LoginRequest } from '@/types/api'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive<LoginRequest>({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async (event?: Event) => {
  // 阻止默认行为
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const success = await userStore.login(loginForm)
    if (success) {
      // 等待一小段时间确保状态更新
      await new Promise(resolve => setTimeout(resolve, 100))

      // 登录成功，跳转到目标页面或仪表板
      const redirect = route.query.redirect as string
      if (redirect) {
        await router.push(redirect)
      } else {
        // 根据用户角色跳转到对应页面
        if (userStore.isTeacher) {
          await router.push('/teacher')
        } else if (userStore.isStudent) {
          await router.push('/student')
        } else {
          await router.push('/dashboard')
        }
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  }
}

// 快速登录
const quickLogin = (username: string, password: string) => {
  loginForm.username = username
  loginForm.password = password
  handleLogin()
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.login-header h1 {
  margin: 0 0 8px;
  font-size: 28px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.login-form {
  padding: 30px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  padding: 0 30px 30px;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
  padding-top: 20px;
}

.test-accounts h4 {
  margin: 0 0 15px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.account-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 12px;
}

.account-item .role {
  color: #409eff;
  font-weight: 500;
  min-width: 30px;
}

.account-item .username,
.account-item .password {
  color: #666;
  font-family: monospace;
  background: white;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #e0e0e0;
}

.account-item .username {
  min-width: 70px;
}

.account-item .password {
  min-width: 80px;
}
</style>
